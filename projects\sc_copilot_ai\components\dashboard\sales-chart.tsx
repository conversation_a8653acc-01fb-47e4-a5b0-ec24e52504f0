'use client'

import React from 'react'
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { BarChart, Bar, XAxis, <PERSON>A<PERSON><PERSON>, ResponsiveContainer } from 'recharts'
import { TrendingUp } from 'lucide-react'

const salesData = [
  { month: 'Feb', value: 45 },
  { month: 'Feb', value: 52 },
  { month: 'Feb', value: 48 },
  { month: 'Feb', value: 61 },
  { month: 'Feb', value: 55 },
  { month: 'Feb', value: 67 },
  { month: 'Feb', value: 73 },
]

export function SalesChart() {
  return (
    <Card className="glass-effect">
      <CardHeader className="pb-2">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 rounded-lg bg-slate-800 flex items-center justify-center">
              <TrendingUp className="w-4 h-4 text-blue-400" />
            </div>
            <div>
              <CardTitle className="text-sm font-medium text-slate-300">Total sale</CardTitle>
              <div className="text-2xl font-bold text-white">90,744</div>
            </div>
          </div>
          <div className="w-8 h-8 rounded-full bg-slate-800 flex items-center justify-center">
            <TrendingUp className="w-4 h-4 text-green-400" />
          </div>
        </div>
      </CardHeader>
      <CardContent className="pt-0">
        <div className="h-32">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={salesData}>
              <XAxis 
                dataKey="month" 
                axisLine={false}
                tickLine={false}
                tick={{ fontSize: 12, fill: '#64748b' }}
              />
              <YAxis hide />
              <Bar 
                dataKey="value" 
                fill="url(#salesGradient)"
                radius={[2, 2, 0, 0]}
              />
              <defs>
                <linearGradient id="salesGradient" x1="0" y1="0" x2="0" y2="1">
                  <stop offset="0%" stopColor="#3b82f6" />
                  <stop offset="100%" stopColor="#1e40af" />
                </linearGradient>
              </defs>
            </BarChart>
          </ResponsiveContainer>
        </div>
      </CardContent>
    </Card>
  )
}