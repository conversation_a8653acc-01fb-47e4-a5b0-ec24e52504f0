'use client'

import React from 'react'
import { Card, CardContent } from '@/components/ui/card'

export function CreditScore() {
  const score = 803
  const maxScore = 850
  const percentage = (score / maxScore) * 100

  return (
    <Card className="glass-effect">
      <CardContent className="p-4">
        <div className="text-center">
          <div className="text-xs text-slate-400 mb-2">Credit rate</div>
          
          {/* Circular Progress */}
          <div className="relative w-20 h-20 mx-auto mb-2">
            <svg className="w-20 h-20 transform -rotate-90" viewBox="0 0 80 80">
              {/* Background Circle */}
              <circle
                cx="40"
                cy="40"
                r="30"
                stroke="#1e293b"
                strokeWidth="8"
                fill="none"
              />
              {/* Progress Circle */}
              <circle
                cx="40"
                cy="40"
                r="30"
                stroke="url(#creditGradient)"
                strokeWidth="8"
                fill="none"
                strokeLinecap="round"
                strokeDasharray={`${percentage * 1.88} 188`}
                className="transition-all duration-1000 ease-out"
              />
              <defs>
                <linearGradient id="creditGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                  <stop offset="0%" stopColor="#8b5cf6" />
                  <stop offset="100%" stopColor="#3b82f6" />
                </linearGradient>
              </defs>
            </svg>
            
            {/* Score Display */}
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="text-center">
                <div className="text-lg font-bold text-white">{score}</div>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}