"use client";
import React from "react";
import { FloatingDock } from "@/components/ui/floating-dock";
import { Home, Package, Settings } from "lucide-react";

export default function FloatingDockTest() {
  const items = [
    {
      title: "Home",
      icon: <Home className="h-full w-full text-neutral-500 dark:text-neutral-300" />,
      href: "#",
    },
    {
      title: "Products",
      icon: <Package className="h-full w-full text-neutral-500 dark:text-neutral-300" />,
      href: "#",
    },
    {
      title: "Settings",
      icon: <Settings className="h-full w-full text-neutral-500 dark:text-neutral-300" />,
      href: "#",
    },
  ];

  return (
    <div className="w-full h-64 bg-slate-700 rounded-lg flex items-center justify-center">
      <div className="text-white mb-8">
        <p className="text-center mb-4">Floating Dock Test</p>
        <FloatingDock items={items} />
      </div>
    </div>
  );
}