'use client'

import React, { useState } from 'react'
import { <PERSON><PERSON> } from '@/components/dashboard/header'
import { AIAssistant } from '@/components/dashboard/ai-assistant'
import { ExamInterface } from '@/components/exam/exam-interface'
import SupplyChainFloatingDock from '@/components/supply-chain-floating-dock'


export default function Dashboard() {
  const [currentView, setCurrentView] = useState<'dashboard' | 'exam'>('exam')

  return (
    <div className="min-h-screen">
      {/* Header */}
      <Header />

      {/* View Toggle */}
      <div className="fixed top-20 right-6 z-50">
        <div className="glass-effect rounded-lg p-2 flex gap-2">
          <button
            onClick={() => setCurrentView('dashboard')}
            className={`px-4 py-2 rounded-md text-sm font-medium transition-all ${
              currentView === 'dashboard'
                ? 'bg-white/20 text-white'
                : 'text-white/60 hover:text-white hover:bg-white/10'
            }`}
          >
            Dashboard
          </button>
          <button
            onClick={() => setCurrentView('exam')}
            className={`px-4 py-2 rounded-md text-sm font-medium transition-all ${
              currentView === 'exam'
                ? 'bg-white/20 text-white'
                : 'text-white/60 hover:text-white hover:bg-white/10'
            }`}
          >
            Exam
          </button>
        </div>
      </div>

      {/* Content */}
      {currentView === 'dashboard' ? (
        <main className="p-4 lg:p-6">
          <div className="max-w-7xl mx-auto">
            <div className="grid grid-cols-12 gap-6">
              {/* AI Assistant - Larger and Centered with Glass Effect */}
              <div className="col-span-12 lg:col-span-10 lg:col-start-2 xl:col-span-8 xl:col-start-3">
                <div className="min-h-[650px] lg:min-h-[750px] xl:min-h-[800px] glass-card">
                  <AIAssistant />
                </div>
              </div>
            </div>
          </div>
        </main>
      ) : (
        <ExamInterface />
      )}

      {/* Supply Chain Floating Dock - Fixed at bottom */}
      <SupplyChainFloatingDock />
    </div>
  )
}