'use client'

import React from 'react'
import { Card, CardContent } from '@/components/ui/card'
import { <PERSON><PERSON>hart, Pie, Cell, ResponsiveContainer } from 'recharts'

const data = [
  { name: 'Primary', value: 60, color: '#3b82f6' },
  { name: 'Secondary', value: 25, color: '#8b5cf6' },
  { name: 'Other', value: 15, color: '#1e293b' },
]

export function DonutChart() {
  return (
    <Card className="glass-effect">
      <CardContent className="p-4">
        <div className="h-32 relative">
          <ResponsiveContainer width="100%" height="100%">
            <PieChart>
              <Pie
                data={data}
                cx="50%"
                cy="50%"
                innerRadius={25}
                outerRadius={45}
                paddingAngle={2}
                dataKey="value"
              >
                {data.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Pie>
            </PieChart>
          </ResponsiveContainer>
          
          {/* Center Label */}
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="text-center">
              <div className="text-xs text-slate-400">Total</div>
              <div className="text-sm font-semibold text-white">100%</div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}