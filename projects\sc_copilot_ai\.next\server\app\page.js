/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5Cumesh%5Cprojects%5Csc_copilot_ai%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cumesh%5Cprojects%5Csc_copilot_ai&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5Cumesh%5Cprojects%5Csc_copilot_ai%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cumesh%5Cprojects%5Csc_copilot_ai&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?5bc9\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(rsc)/./app/page.tsx\")), \"C:\\\\Users\\\\<USER>\\\\projects\\\\sc_copilot_ai\\\\app\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\projects\\\\sc_copilot_ai\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\projects\\\\sc_copilot_ai\\\\app\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5Cumesh%5Cprojects%5Csc_copilot_ai%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cumesh%5Cprojects%5Csc_copilot_ai&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cumesh%5Cprojects%5Csc_copilot_ai%5Capp%5Cpage.tsx&server=true!":
/*!*******************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cumesh%5Cprojects%5Csc_copilot_ai%5Capp%5Cpage.tsx&server=true! ***!
  \*******************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(ssr)/./app/page.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDdW1lc2glNUNwcm9qZWN0cyU1Q3NjX2NvcGlsb3RfYWklNUNhcHAlNUNwYWdlLnRzeCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zdXBwbHktY2hhaW4tZGFzaGJvYXJkLz9hMzQwIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcdW1lc2hcXFxccHJvamVjdHNcXFxcc2NfY29waWxvdF9haVxcXFxhcHBcXFxccGFnZS50c3hcIikiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cumesh%5Cprojects%5Csc_copilot_ai%5Capp%5Cpage.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cumesh%5Cprojects%5Csc_copilot_ai%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5Cumesh%5Cprojects%5Csc_copilot_ai%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5Cumesh%5Cprojects%5Csc_copilot_ai%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5Cumesh%5Cprojects%5Csc_copilot_ai%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5Cumesh%5Cprojects%5Csc_copilot_ai%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5Cumesh%5Cprojects%5Csc_copilot_ai%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cumesh%5Cprojects%5Csc_copilot_ai%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5Cumesh%5Cprojects%5Csc_copilot_ai%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5Cumesh%5Cprojects%5Csc_copilot_ai%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5Cumesh%5Cprojects%5Csc_copilot_ai%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5Cumesh%5Cprojects%5Csc_copilot_ai%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5Cumesh%5Cprojects%5Csc_copilot_ai%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cumesh%5Cprojects%5Csc_copilot_ai%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5Cumesh%5Cprojects%5Csc_copilot_ai%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5Cumesh%5Cprojects%5Csc_copilot_ai%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5Cumesh%5Cprojects%5Csc_copilot_ai%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5Cumesh%5Cprojects%5Csc_copilot_ai%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5Cumesh%5Cprojects%5Csc_copilot_ai%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cumesh%5Cprojects%5Csc_copilot_ai%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22app%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=C%3A%5CUsers%5Cumesh%5Cprojects%5Csc_copilot_ai%5Capp%5Cglobals.css&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cumesh%5Cprojects%5Csc_copilot_ai%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22app%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=C%3A%5CUsers%5Cumesh%5Cprojects%5Csc_copilot_ai%5Capp%5Cglobals.css&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Dashboard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_dashboard_header__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/dashboard/header */ \"(ssr)/./components/dashboard/header.tsx\");\n/* harmony import */ var _components_dashboard_ai_assistant__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/dashboard/ai-assistant */ \"(ssr)/./components/dashboard/ai-assistant.tsx\");\n/* harmony import */ var _components_supply_chain_floating_dock__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/supply-chain-floating-dock */ \"(ssr)/./components/supply-chain-floating-dock.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction Dashboard() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_header__WEBPACK_IMPORTED_MODULE_2__.Header, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\sc_copilot_ai\\\\app\\\\page.tsx\",\n                lineNumber: 13,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"p-4 lg:p-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-12 gap-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"col-span-12 lg:col-span-10 lg:col-start-2 xl:col-span-8 xl:col-start-3\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"min-h-[650px] lg:min-h-[750px] xl:min-h-[800px] glass-card\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_ai_assistant__WEBPACK_IMPORTED_MODULE_3__.AIAssistant, {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\sc_copilot_ai\\\\app\\\\page.tsx\",\n                                    lineNumber: 22,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\sc_copilot_ai\\\\app\\\\page.tsx\",\n                                lineNumber: 21,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\sc_copilot_ai\\\\app\\\\page.tsx\",\n                            lineNumber: 20,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\sc_copilot_ai\\\\app\\\\page.tsx\",\n                        lineNumber: 18,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\sc_copilot_ai\\\\app\\\\page.tsx\",\n                    lineNumber: 17,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\sc_copilot_ai\\\\app\\\\page.tsx\",\n                lineNumber: 16,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_supply_chain_floating_dock__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\sc_copilot_ai\\\\app\\\\page.tsx\",\n                lineNumber: 30,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\sc_copilot_ai\\\\app\\\\page.tsx\",\n        lineNumber: 11,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/page.tsx\n");

/***/ }),

/***/ "(ssr)/./components/dashboard/ai-assistant.tsx":
/*!***********************************************!*\
  !*** ./components/dashboard/ai-assistant.tsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AIAssistant: () => (/* binding */ AIAssistant)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _components_ui_multi_step_loader__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/multi-step-loader */ \"(ssr)/./components/ui/multi-step-loader.tsx\");\n/* harmony import */ var _components_ui_hover_border_gradient__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/hover-border-gradient */ \"(ssr)/./components/ui/hover-border-gradient.tsx\");\n/* harmony import */ var _barrel_optimize_names_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* __next_internal_client_entry_do_not_use__ AIAssistant auto */ \n\n\n\n\n\nconst SupplyChainLogo = ()=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        width: \"20\",\n        height: \"20\",\n        viewBox: \"0 0 24 24\",\n        fill: \"none\",\n        xmlns: \"http://www.w3.org/2000/svg\",\n        className: \"h-4 w-4 text-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M12 2L2 7L12 12L22 7L12 2Z\",\n                stroke: \"currentColor\",\n                strokeWidth: \"2\",\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\sc_copilot_ai\\\\components\\\\dashboard\\\\ai-assistant.tsx\",\n                lineNumber: 18,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M2 17L12 22L22 17\",\n                stroke: \"currentColor\",\n                strokeWidth: \"2\",\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\sc_copilot_ai\\\\components\\\\dashboard\\\\ai-assistant.tsx\",\n                lineNumber: 25,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M2 12L12 17L22 12\",\n                stroke: \"currentColor\",\n                strokeWidth: \"2\",\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\sc_copilot_ai\\\\components\\\\dashboard\\\\ai-assistant.tsx\",\n                lineNumber: 32,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\sc_copilot_ai\\\\components\\\\dashboard\\\\ai-assistant.tsx\",\n        lineNumber: 10,\n        columnNumber: 5\n    }, undefined);\n};\nfunction AIAssistant() {\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const loadingStates = [\n        {\n            text: \"Analyzing supply chain data\"\n        },\n        {\n            text: \"Processing inventory levels\"\n        },\n        {\n            text: \"Calculating demand forecasts\"\n        },\n        {\n            text: \"Optimizing delivery routes\"\n        },\n        {\n            text: \"Updating supplier metrics\"\n        },\n        {\n            text: \"Generating insights report\"\n        },\n        {\n            text: \"Finalizing recommendations\"\n        },\n        {\n            text: \"Dashboard ready!\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n        className: \"relative h-full w-full p-8 lg:p-12 flex flex-col items-center justify-center bg-gradient-to-br from-slate-900/95 to-slate-800/95 backdrop-blur-xl rounded-3xl overflow-hidden\",\n        style: {\n            border: \"1px solid rgba(59, 130, 246, 0.3)\",\n            boxShadow: \"0 0 20px rgba(59, 130, 246, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.1)\",\n            minHeight: \"650px\"\n        },\n        animate: {\n            boxShadow: [\n                \"0 0 20px rgba(59, 130, 246, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.1)\",\n                \"0 0 30px rgba(59, 130, 246, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.1)\",\n                \"0 0 20px rgba(59, 130, 246, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.1)\"\n            ]\n        },\n        transition: {\n            duration: 3,\n            repeat: Infinity,\n            ease: \"easeInOut\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative mb-8 lg:mb-12\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"ai-brain-sphere scale-125 lg:scale-150\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"brain-container\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"brain-mesh\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\sc_copilot_ai\\\\components\\\\dashboard\\\\ai-assistant.tsx\",\n                                lineNumber: 97,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"brain-waves\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\sc_copilot_ai\\\\components\\\\dashboard\\\\ai-assistant.tsx\",\n                                lineNumber: 98,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"brain-core\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\sc_copilot_ai\\\\components\\\\dashboard\\\\ai-assistant.tsx\",\n                                lineNumber: 99,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"brain-particles\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"particle\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\sc_copilot_ai\\\\components\\\\dashboard\\\\ai-assistant.tsx\",\n                                        lineNumber: 101,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"particle\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\sc_copilot_ai\\\\components\\\\dashboard\\\\ai-assistant.tsx\",\n                                        lineNumber: 102,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"particle\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\sc_copilot_ai\\\\components\\\\dashboard\\\\ai-assistant.tsx\",\n                                        lineNumber: 103,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"particle\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\sc_copilot_ai\\\\components\\\\dashboard\\\\ai-assistant.tsx\",\n                                        lineNumber: 104,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"particle\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\sc_copilot_ai\\\\components\\\\dashboard\\\\ai-assistant.tsx\",\n                                        lineNumber: 105,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"particle\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\sc_copilot_ai\\\\components\\\\dashboard\\\\ai-assistant.tsx\",\n                                        lineNumber: 106,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\sc_copilot_ai\\\\components\\\\dashboard\\\\ai-assistant.tsx\",\n                                lineNumber: 100,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\sc_copilot_ai\\\\components\\\\dashboard\\\\ai-assistant.tsx\",\n                        lineNumber: 96,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\sc_copilot_ai\\\\components\\\\dashboard\\\\ai-assistant.tsx\",\n                    lineNumber: 95,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\sc_copilot_ai\\\\components\\\\dashboard\\\\ai-assistant.tsx\",\n                lineNumber: 93,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_multi_step_loader__WEBPACK_IMPORTED_MODULE_2__.MultiStepLoader, {\n                    loadingStates: loadingStates,\n                    loading: loading,\n                    duration: 2000\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\sc_copilot_ai\\\\components\\\\dashboard\\\\ai-assistant.tsx\",\n                    lineNumber: 114,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\sc_copilot_ai\\\\components\\\\dashboard\\\\ai-assistant.tsx\",\n                lineNumber: 113,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_hover_border_gradient__WEBPACK_IMPORTED_MODULE_3__.HoverBorderGradient, {\n                containerClassName: \"rounded-full mb-6\",\n                as: \"button\",\n                className: \"dark:bg-slate-800 bg-slate-100 text-white dark:text-white flex items-center space-x-3 px-8 py-3 text-lg\",\n                onClick: ()=>setLoading(true),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SupplyChainLogo, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\sc_copilot_ai\\\\components\\\\dashboard\\\\ai-assistant.tsx\",\n                        lineNumber: 123,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: \"Start AI Analysis\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\sc_copilot_ai\\\\components\\\\dashboard\\\\ai-assistant.tsx\",\n                        lineNumber: 124,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\sc_copilot_ai\\\\components\\\\dashboard\\\\ai-assistant.tsx\",\n                lineNumber: 117,\n                columnNumber: 7\n            }, this),\n            loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                className: \"fixed top-4 right-4 text-black dark:text-white z-[120]\",\n                onClick: ()=>setLoading(false),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"h-10 w-10\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\sc_copilot_ai\\\\components\\\\dashboard\\\\ai-assistant.tsx\",\n                    lineNumber: 132,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\sc_copilot_ai\\\\components\\\\dashboard\\\\ai-assistant.tsx\",\n                lineNumber: 128,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center max-w-2xl\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl lg:text-3xl font-bold text-white mb-4\",\n                        children: \"AI Supply Chain Assistant\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\sc_copilot_ai\\\\components\\\\dashboard\\\\ai-assistant.tsx\",\n                        lineNumber: 137,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg lg:text-xl font-semibold text-blue-300 mb-4\",\n                        children: \"Analyze product sales over last year\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\sc_copilot_ai\\\\components\\\\dashboard\\\\ai-assistant.tsx\",\n                        lineNumber: 140,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-300 text-base lg:text-lg mb-6 leading-relaxed\",\n                        children: \"Compare revenue, quality, sales and brand performance with advanced AI insights. Get real-time analytics, predictive forecasting, and optimization recommendations for your entire supply chain ecosystem.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\sc_copilot_ai\\\\components\\\\dashboard\\\\ai-assistant.tsx\",\n                        lineNumber: 143,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 lg:grid-cols-4 gap-4 text-sm text-gray-400\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-8 h-8 bg-blue-500/20 rounded-full flex items-center justify-center mb-2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-blue-400\",\n                                            children: \"\\uD83D\\uDCCA\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\sc_copilot_ai\\\\components\\\\dashboard\\\\ai-assistant.tsx\",\n                                            lineNumber: 151,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\sc_copilot_ai\\\\components\\\\dashboard\\\\ai-assistant.tsx\",\n                                        lineNumber: 150,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Analytics\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\sc_copilot_ai\\\\components\\\\dashboard\\\\ai-assistant.tsx\",\n                                        lineNumber: 153,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\sc_copilot_ai\\\\components\\\\dashboard\\\\ai-assistant.tsx\",\n                                lineNumber: 149,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-8 h-8 bg-blue-500/20 rounded-full flex items-center justify-center mb-2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-blue-400\",\n                                            children: \"\\uD83D\\uDD2E\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\sc_copilot_ai\\\\components\\\\dashboard\\\\ai-assistant.tsx\",\n                                            lineNumber: 157,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\sc_copilot_ai\\\\components\\\\dashboard\\\\ai-assistant.tsx\",\n                                        lineNumber: 156,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Forecasting\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\sc_copilot_ai\\\\components\\\\dashboard\\\\ai-assistant.tsx\",\n                                        lineNumber: 159,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\sc_copilot_ai\\\\components\\\\dashboard\\\\ai-assistant.tsx\",\n                                lineNumber: 155,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-8 h-8 bg-blue-500/20 rounded-full flex items-center justify-center mb-2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-blue-400\",\n                                            children: \"⚡\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\sc_copilot_ai\\\\components\\\\dashboard\\\\ai-assistant.tsx\",\n                                            lineNumber: 163,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\sc_copilot_ai\\\\components\\\\dashboard\\\\ai-assistant.tsx\",\n                                        lineNumber: 162,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Optimization\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\sc_copilot_ai\\\\components\\\\dashboard\\\\ai-assistant.tsx\",\n                                        lineNumber: 165,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\sc_copilot_ai\\\\components\\\\dashboard\\\\ai-assistant.tsx\",\n                                lineNumber: 161,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-8 h-8 bg-blue-500/20 rounded-full flex items-center justify-center mb-2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-blue-400\",\n                                            children: \"\\uD83C\\uDFAF\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\sc_copilot_ai\\\\components\\\\dashboard\\\\ai-assistant.tsx\",\n                                            lineNumber: 169,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\sc_copilot_ai\\\\components\\\\dashboard\\\\ai-assistant.tsx\",\n                                        lineNumber: 168,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Insights\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\sc_copilot_ai\\\\components\\\\dashboard\\\\ai-assistant.tsx\",\n                                        lineNumber: 171,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\sc_copilot_ai\\\\components\\\\dashboard\\\\ai-assistant.tsx\",\n                                lineNumber: 167,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\sc_copilot_ai\\\\components\\\\dashboard\\\\ai-assistant.tsx\",\n                        lineNumber: 148,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\sc_copilot_ai\\\\components\\\\dashboard\\\\ai-assistant.tsx\",\n                lineNumber: 136,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\sc_copilot_ai\\\\components\\\\dashboard\\\\ai-assistant.tsx\",\n        lineNumber: 73,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/dashboard/ai-assistant.tsx\n");

/***/ }),

/***/ "(ssr)/./components/dashboard/header.tsx":
/*!*****************************************!*\
  !*** ./components/dashboard/header.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Header: () => (/* binding */ Header)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Bell_Calendar_MessageCircle_User_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Calendar,MessageCircle,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Calendar_MessageCircle_User_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Calendar,MessageCircle,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/message-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Calendar_MessageCircle_User_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Calendar,MessageCircle,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Calendar_MessageCircle_User_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Calendar,MessageCircle,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* __next_internal_client_entry_do_not_use__ Header auto */ \n\n\nfunction Header() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"h-16 bg-slate-900/30 backdrop-blur-xl border-b border-slate-800 flex items-center justify-between px-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: \"text-xl font-semibold text-white\",\n                    children: \"Product Sales Dashboard\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\sc_copilot_ai\\\\components\\\\dashboard\\\\header.tsx\",\n                    lineNumber: 10,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\sc_copilot_ai\\\\components\\\\dashboard\\\\header.tsx\",\n                lineNumber: 9,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        className: \"w-10 h-10 rounded-lg bg-slate-800/50 hover:bg-slate-700/50 flex items-center justify-center text-slate-400 hover:text-white transition-colors\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Calendar_MessageCircle_User_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            className: \"w-5 h-5\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\sc_copilot_ai\\\\components\\\\dashboard\\\\header.tsx\",\n                            lineNumber: 16,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\sc_copilot_ai\\\\components\\\\dashboard\\\\header.tsx\",\n                        lineNumber: 15,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        className: \"w-10 h-10 rounded-lg bg-slate-800/50 hover:bg-slate-700/50 flex items-center justify-center text-slate-400 hover:text-white transition-colors\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Calendar_MessageCircle_User_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            className: \"w-5 h-5\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\sc_copilot_ai\\\\components\\\\dashboard\\\\header.tsx\",\n                            lineNumber: 20,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\sc_copilot_ai\\\\components\\\\dashboard\\\\header.tsx\",\n                        lineNumber: 19,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        className: \"w-10 h-10 rounded-lg bg-slate-800/50 hover:bg-slate-700/50 flex items-center justify-center text-slate-400 hover:text-white transition-colors relative\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Calendar_MessageCircle_User_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                className: \"w-5 h-5\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\sc_copilot_ai\\\\components\\\\dashboard\\\\header.tsx\",\n                                lineNumber: 24,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\sc_copilot_ai\\\\components\\\\dashboard\\\\header.tsx\",\n                                lineNumber: 25,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\sc_copilot_ai\\\\components\\\\dashboard\\\\header.tsx\",\n                        lineNumber: 23,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-10 h-10 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Calendar_MessageCircle_User_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            className: \"w-5 h-5 text-white\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\sc_copilot_ai\\\\components\\\\dashboard\\\\header.tsx\",\n                            lineNumber: 30,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\sc_copilot_ai\\\\components\\\\dashboard\\\\header.tsx\",\n                        lineNumber: 29,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\sc_copilot_ai\\\\components\\\\dashboard\\\\header.tsx\",\n                lineNumber: 13,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\sc_copilot_ai\\\\components\\\\dashboard\\\\header.tsx\",\n        lineNumber: 8,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/dashboard/header.tsx\n");

/***/ }),

/***/ "(ssr)/./components/supply-chain-floating-dock.tsx":
/*!***************************************************!*\
  !*** ./components/supply-chain-floating-dock.tsx ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SupplyChainFloatingDock)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_floating_dock__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/floating-dock */ \"(ssr)/./components/ui/floating-dock.tsx\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Home_MessageSquare_Package_Settings_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Home,MessageSquare,Package,Settings,Truck,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/home.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Home_MessageSquare_Package_Settings_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Home,MessageSquare,Package,Settings,Truck,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Home_MessageSquare_Package_Settings_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Home,MessageSquare,Package,Settings,Truck,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/truck.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Home_MessageSquare_Package_Settings_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Home,MessageSquare,Package,Settings,Truck,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Home_MessageSquare_Package_Settings_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Home,MessageSquare,Package,Settings,Truck,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/message-square.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Home_MessageSquare_Package_Settings_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Home,MessageSquare,Package,Settings,Truck,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Home_MessageSquare_Package_Settings_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Home,MessageSquare,Package,Settings,Truck,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n\n\n\n\nfunction SupplyChainFloatingDock() {\n    const links = [\n        {\n            title: \"Dashboard\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Home_MessageSquare_Package_Settings_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                className: \"h-full w-full text-slate-300 dark:text-slate-300\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\sc_copilot_ai\\\\components\\\\supply-chain-floating-dock.tsx\",\n                lineNumber: 18,\n                columnNumber: 9\n            }, this),\n            href: \"/\"\n        },\n        {\n            title: \"Inventory\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Home_MessageSquare_Package_Settings_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                className: \"h-full w-full text-slate-300 dark:text-slate-300\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\sc_copilot_ai\\\\components\\\\supply-chain-floating-dock.tsx\",\n                lineNumber: 25,\n                columnNumber: 9\n            }, this),\n            href: \"#inventory\"\n        },\n        {\n            title: \"Logistics\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Home_MessageSquare_Package_Settings_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                className: \"h-full w-full text-slate-300 dark:text-slate-300\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\sc_copilot_ai\\\\components\\\\supply-chain-floating-dock.tsx\",\n                lineNumber: 32,\n                columnNumber: 9\n            }, this),\n            href: \"#logistics\"\n        },\n        {\n            title: \"Analytics\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Home_MessageSquare_Package_Settings_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                className: \"h-full w-full text-slate-300 dark:text-slate-300\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\sc_copilot_ai\\\\components\\\\supply-chain-floating-dock.tsx\",\n                lineNumber: 39,\n                columnNumber: 9\n            }, this),\n            href: \"#analytics\"\n        },\n        {\n            title: \"AI Assistant\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Home_MessageSquare_Package_Settings_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                className: \"h-full w-full text-blue-400 dark:text-blue-400\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\sc_copilot_ai\\\\components\\\\supply-chain-floating-dock.tsx\",\n                lineNumber: 46,\n                columnNumber: 9\n            }, this),\n            href: \"#ai-chat\"\n        },\n        {\n            title: \"Suppliers\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Home_MessageSquare_Package_Settings_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                className: \"h-full w-full text-slate-300 dark:text-slate-300\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\sc_copilot_ai\\\\components\\\\supply-chain-floating-dock.tsx\",\n                lineNumber: 53,\n                columnNumber: 9\n            }, this),\n            href: \"#suppliers\"\n        },\n        {\n            title: \"Settings\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Home_MessageSquare_Package_Settings_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                className: \"h-full w-full text-slate-300 dark:text-slate-300\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\sc_copilot_ai\\\\components\\\\supply-chain-floating-dock.tsx\",\n                lineNumber: 60,\n                columnNumber: 9\n            }, this),\n            href: \"#settings\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed bottom-6 left-1/2 transform -translate-x-1/2 z-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_floating_dock__WEBPACK_IMPORTED_MODULE_2__.FloatingDock, {\n            items: links\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\sc_copilot_ai\\\\components\\\\supply-chain-floating-dock.tsx\",\n            lineNumber: 68,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\sc_copilot_ai\\\\components\\\\supply-chain-floating-dock.tsx\",\n        lineNumber: 67,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/supply-chain-floating-dock.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/floating-dock.tsx":
/*!*****************************************!*\
  !*** ./components/ui/floating-dock.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FloatingDock: () => (/* binding */ FloatingDock)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n/* harmony import */ var _barrel_optimize_names_Menu_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Menu!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/value/use-motion-value.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/value/use-transform.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/value/use-spring.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\n\n\nconst FloatingDock = ({ items, desktopClassName, mobileClassName })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FloatingDockDesktop, {\n                items: items,\n                className: desktopClassName\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\sc_copilot_ai\\\\components\\\\ui\\\\floating-dock.tsx\",\n                lineNumber: 25,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FloatingDockMobile, {\n                items: items,\n                className: mobileClassName\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\sc_copilot_ai\\\\components\\\\ui\\\\floating-dock.tsx\",\n                lineNumber: 26,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\nconst FloatingDockMobile = ({ items, className })=>{\n    const [open, setOpen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(\"relative block md:hidden\", className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.AnimatePresence, {\n                children: open && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                    layoutId: \"nav\",\n                    className: \"absolute inset-x-0 bottom-full mb-2 flex flex-col gap-2\",\n                    children: items.map((item, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 10\n                            },\n                            animate: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            exit: {\n                                opacity: 0,\n                                y: 10,\n                                transition: {\n                                    delay: idx * 0.05\n                                }\n                            },\n                            transition: {\n                                delay: (items.length - 1 - idx) * 0.05\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: item.href,\n                                className: \"flex h-10 w-10 items-center justify-center rounded-full glass-effect hover:bg-blue-500/20 transition-colors duration-200\",\n                                style: {\n                                    border: \"1px solid rgba(59, 130, 246, 0.3)\",\n                                    boxShadow: \"0 0 8px rgba(59, 130, 246, 0.15)\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-4 w-4\",\n                                    children: item.icon\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\sc_copilot_ai\\\\components\\\\ui\\\\floating-dock.tsx\",\n                                    lineNumber: 73,\n                                    columnNumber: 19\n                                }, undefined)\n                            }, item.title, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\sc_copilot_ai\\\\components\\\\ui\\\\floating-dock.tsx\",\n                                lineNumber: 64,\n                                columnNumber: 17\n                            }, undefined)\n                        }, item.title, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\sc_copilot_ai\\\\components\\\\ui\\\\floating-dock.tsx\",\n                            lineNumber: 48,\n                            columnNumber: 15\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\sc_copilot_ai\\\\components\\\\ui\\\\floating-dock.tsx\",\n                    lineNumber: 43,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\sc_copilot_ai\\\\components\\\\ui\\\\floating-dock.tsx\",\n                lineNumber: 41,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: ()=>setOpen(!open),\n                className: \"flex h-10 w-10 items-center justify-center rounded-full glass-effect hover:bg-blue-500/20 transition-colors duration-200\",\n                style: {\n                    border: \"1px solid rgba(59, 130, 246, 0.3)\",\n                    boxShadow: \"0 0 8px rgba(59, 130, 246, 0.15)\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"h-5 w-5 text-slate-300\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\sc_copilot_ai\\\\components\\\\ui\\\\floating-dock.tsx\",\n                    lineNumber: 88,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\sc_copilot_ai\\\\components\\\\ui\\\\floating-dock.tsx\",\n                lineNumber: 80,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\sc_copilot_ai\\\\components\\\\ui\\\\floating-dock.tsx\",\n        lineNumber: 40,\n        columnNumber: 5\n    }, undefined);\n};\nconst FloatingDockDesktop = ({ items, className })=>{\n    let mouseX = (0,framer_motion__WEBPACK_IMPORTED_MODULE_6__.useMotionValue)(Infinity);\n    const handleMouseMove = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((e)=>{\n        mouseX.set(e.pageX);\n    }, [\n        mouseX\n    ]);\n    const handleMouseLeave = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(()=>{\n        mouseX.set(Infinity);\n    }, [\n        mouseX\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n        onMouseMove: handleMouseMove,\n        onMouseLeave: handleMouseLeave,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(\"mx-auto hidden h-16 items-end gap-3 rounded-2xl px-4 pb-3 md:flex glass-effect\", className),\n        style: {\n            border: \"1px solid rgba(59, 130, 246, 0.3)\",\n            boxShadow: \"0 0 15px rgba(59, 130, 246, 0.15), inset 0 1px 0 rgba(255, 255, 255, 0.1)\",\n            background: \"rgba(15, 23, 42, 0.85)\",\n            backdropFilter: \"blur(16px)\"\n        },\n        children: items.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconContainer, {\n                mouseX: mouseX,\n                ...item\n            }, item.title, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\sc_copilot_ai\\\\components\\\\ui\\\\floating-dock.tsx\",\n                lineNumber: 127,\n                columnNumber: 9\n            }, undefined))\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\sc_copilot_ai\\\\components\\\\ui\\\\floating-dock.tsx\",\n        lineNumber: 112,\n        columnNumber: 5\n    }, undefined);\n};\nconst IconContainer = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_2__.memo)(function IconContainer({ mouseX, title, icon, href }) {\n    let ref = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    let distance = (0,framer_motion__WEBPACK_IMPORTED_MODULE_7__.useTransform)(mouseX, (val)=>{\n        let bounds = ref.current?.getBoundingClientRect() ?? {\n            x: 0,\n            width: 0\n        };\n        return val - bounds.x - bounds.width / 2;\n    });\n    let widthTransform = (0,framer_motion__WEBPACK_IMPORTED_MODULE_7__.useTransform)(distance, [\n        -100,\n        0,\n        100\n    ], [\n        40,\n        70,\n        40\n    ]);\n    let heightTransform = (0,framer_motion__WEBPACK_IMPORTED_MODULE_7__.useTransform)(distance, [\n        -100,\n        0,\n        100\n    ], [\n        40,\n        70,\n        40\n    ]);\n    let widthTransformIcon = (0,framer_motion__WEBPACK_IMPORTED_MODULE_7__.useTransform)(distance, [\n        -100,\n        0,\n        100\n    ], [\n        20,\n        35,\n        20\n    ]);\n    let heightTransformIcon = (0,framer_motion__WEBPACK_IMPORTED_MODULE_7__.useTransform)(distance, [\n        -100,\n        0,\n        100\n    ], [\n        20,\n        35,\n        20\n    ]);\n    let width = (0,framer_motion__WEBPACK_IMPORTED_MODULE_8__.useSpring)(widthTransform, {\n        mass: 0.05,\n        stiffness: 300,\n        damping: 15\n    });\n    let height = (0,framer_motion__WEBPACK_IMPORTED_MODULE_8__.useSpring)(heightTransform, {\n        mass: 0.05,\n        stiffness: 300,\n        damping: 15\n    });\n    let widthIcon = (0,framer_motion__WEBPACK_IMPORTED_MODULE_8__.useSpring)(widthTransformIcon, {\n        mass: 0.05,\n        stiffness: 300,\n        damping: 15\n    });\n    let heightIcon = (0,framer_motion__WEBPACK_IMPORTED_MODULE_8__.useSpring)(heightTransformIcon, {\n        mass: 0.05,\n        stiffness: 300,\n        damping: 15\n    });\n    const [hovered, setHovered] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n        href: href,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n            ref: ref,\n            style: {\n                width,\n                height\n            },\n            onMouseEnter: ()=>setHovered(true),\n            onMouseLeave: ()=>setHovered(false),\n            className: \"relative flex aspect-square items-center justify-center rounded-full glass-effect hover:bg-blue-500/20 transition-colors duration-200\",\n            whileHover: {\n                boxShadow: \"0 0 15px rgba(59, 130, 246, 0.3)\"\n            },\n            transition: {\n                duration: 0.2\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.AnimatePresence, {\n                    children: hovered && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 8,\n                            x: \"-50%\"\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0,\n                            x: \"-50%\"\n                        },\n                        exit: {\n                            opacity: 0,\n                            y: 4,\n                            x: \"-50%\"\n                        },\n                        transition: {\n                            duration: 0.15\n                        },\n                        className: \"absolute -top-8 left-1/2 w-fit rounded-md px-2 py-0.5 text-xs whitespace-pre text-white glass-effect\",\n                        style: {\n                            border: \"1px solid rgba(59, 130, 246, 0.3)\",\n                            boxShadow: \"0 0 8px rgba(59, 130, 246, 0.2)\",\n                            background: \"rgba(15, 23, 42, 0.95)\",\n                            backdropFilter: \"blur(8px)\"\n                        },\n                        children: title\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\sc_copilot_ai\\\\components\\\\ui\\\\floating-dock.tsx\",\n                        lineNumber: 196,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\sc_copilot_ai\\\\components\\\\ui\\\\floating-dock.tsx\",\n                    lineNumber: 194,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                    style: {\n                        width: widthIcon,\n                        height: heightIcon\n                    },\n                    className: \"flex items-center justify-center\",\n                    children: icon\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\sc_copilot_ai\\\\components\\\\ui\\\\floating-dock.tsx\",\n                    lineNumber: 213,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\sc_copilot_ai\\\\components\\\\ui\\\\floating-dock.tsx\",\n            lineNumber: 183,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\sc_copilot_ai\\\\components\\\\ui\\\\floating-dock.tsx\",\n        lineNumber: 182,\n        columnNumber: 5\n    }, this);\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/floating-dock.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/hover-border-gradient.tsx":
/*!*************************************************!*\
  !*** ./components/ui/hover-border-gradient.tsx ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HoverBorderGradient: () => (/* binding */ HoverBorderGradient)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ HoverBorderGradient auto */ \n\n\n\nfunction HoverBorderGradient({ children, containerClassName, className, as: Tag = \"button\", duration = 1, clockwise = true, ...props }) {\n    const [hovered, setHovered] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Tag, {\n        onMouseEnter: ()=>setHovered(true),\n        onMouseLeave: ()=>setHovered(false),\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"relative flex items-center justify-center rounded-full p-[2px] overflow-hidden\", containerClassName),\n        ...props,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                className: \"absolute inset-0 rounded-full\",\n                style: {\n                    background: \"linear-gradient(45deg, #3b82f6, #8b5cf6, #06b6d4, #3b82f6)\",\n                    backgroundSize: \"300% 300%\"\n                },\n                animate: {\n                    backgroundPosition: clockwise ? [\n                        \"0% 50%\",\n                        \"100% 50%\",\n                        \"0% 50%\"\n                    ] : [\n                        \"100% 50%\",\n                        \"0% 50%\",\n                        \"100% 50%\"\n                    ]\n                },\n                transition: {\n                    duration: duration * 3,\n                    repeat: Infinity,\n                    ease: \"linear\"\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\sc_copilot_ai\\\\components\\\\ui\\\\hover-border-gradient.tsx\",\n                lineNumber: 36,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                className: \"absolute inset-0 rounded-full\",\n                style: {\n                    background: \"radial-gradient(circle, rgba(59, 130, 246, 0.5) 0%, transparent 70%)\"\n                },\n                initial: {\n                    opacity: 0,\n                    scale: 0.8\n                },\n                animate: {\n                    opacity: hovered ? 1 : 0,\n                    scale: hovered ? 1.1 : 0.8\n                },\n                transition: {\n                    duration: 0.3\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\sc_copilot_ai\\\\components\\\\ui\\\\hover-border-gradient.tsx\",\n                lineNumber: 55,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"relative z-10 flex items-center justify-center rounded-full px-4 py-2 bg-black text-white\", className),\n                children: children\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\sc_copilot_ai\\\\components\\\\ui\\\\hover-border-gradient.tsx\",\n                lineNumber: 69,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\sc_copilot_ai\\\\components\\\\ui\\\\hover-border-gradient.tsx\",\n        lineNumber: 26,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/hover-border-gradient.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/multi-step-loader.tsx":
/*!*********************************************!*\
  !*** ./components/ui/multi-step-loader.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MultiStepLoader: () => (/* binding */ MultiStepLoader)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ MultiStepLoader auto */ \n\n\n\nconst CheckIcon = ({ className })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        xmlns: \"http://www.w3.org/2000/svg\",\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        strokeWidth: 1.5,\n        stroke: \"currentColor\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(\"w-6 h-6 \", className),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n            d: \"M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\sc_copilot_ai\\\\components\\\\ui\\\\multi-step-loader.tsx\",\n            lineNumber: 16,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\sc_copilot_ai\\\\components\\\\ui\\\\multi-step-loader.tsx\",\n        lineNumber: 8,\n        columnNumber: 5\n    }, undefined);\n};\nconst CheckFilled = ({ className })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        xmlns: \"http://www.w3.org/2000/svg\",\n        viewBox: \"0 0 24 24\",\n        fill: \"currentColor\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(\"w-6 h-6 \", className),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n            fillRule: \"evenodd\",\n            d: \"M2.25 12c0-5.385 4.365-9.75 9.75-9.75s9.75 4.365 9.75 9.75-4.365 9.75-9.75 9.75S2.25 17.385 2.25 12Zm13.36-1.814a.75.75 0 1 0-1.22-.872l-3.236 4.53L9.53 12.22a.75.75 0 0 0-1.06 1.06l2.25 2.25a.75.75 0 0 0 1.14-.094l3.75-5.25Z\",\n            clipRule: \"evenodd\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\sc_copilot_ai\\\\components\\\\ui\\\\multi-step-loader.tsx\",\n            lineNumber: 29,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\sc_copilot_ai\\\\components\\\\ui\\\\multi-step-loader.tsx\",\n        lineNumber: 23,\n        columnNumber: 5\n    }, undefined);\n};\nconst LoaderCore = ({ loadingStates, value = 0 })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex relative justify-start max-w-xl mx-auto flex-col mt-40\",\n        children: loadingStates.map((loadingState, index)=>{\n            const distance = Math.abs(index - value);\n            const opacity = Math.max(1 - distance * 0.2, 0); // Minimum opacity is 0, keep it 0.2 if you're sane.\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(\"text-left flex gap-2 mb-4\"),\n                initial: {\n                    opacity: 0,\n                    y: -(value * 40)\n                },\n                animate: {\n                    opacity: opacity,\n                    y: -(value * 40)\n                },\n                transition: {\n                    duration: 0.5\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            index > value && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CheckIcon, {\n                                className: \"text-black dark:text-white\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\sc_copilot_ai\\\\components\\\\ui\\\\multi-step-loader.tsx\",\n                                lineNumber: 65,\n                                columnNumber: 17\n                            }, undefined),\n                            index <= value && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CheckFilled, {\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(\"text-black dark:text-white\", value === index && \"text-black dark:text-lime-500 opacity-100\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\sc_copilot_ai\\\\components\\\\ui\\\\multi-step-loader.tsx\",\n                                lineNumber: 68,\n                                columnNumber: 17\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\sc_copilot_ai\\\\components\\\\ui\\\\multi-step-loader.tsx\",\n                        lineNumber: 63,\n                        columnNumber: 13\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(\"text-black dark:text-white\", value === index && \"text-black dark:text-lime-500 opacity-100\"),\n                        children: loadingState.text\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\sc_copilot_ai\\\\components\\\\ui\\\\multi-step-loader.tsx\",\n                        lineNumber: 77,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, index, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\sc_copilot_ai\\\\components\\\\ui\\\\multi-step-loader.tsx\",\n                lineNumber: 56,\n                columnNumber: 11\n            }, undefined);\n        })\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\sc_copilot_ai\\\\components\\\\ui\\\\multi-step-loader.tsx\",\n        lineNumber: 50,\n        columnNumber: 5\n    }, undefined);\n};\nconst MultiStepLoader = ({ loadingStates, loading, duration = 2000, loop = true })=>{\n    const [currentState, setCurrentState] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (!loading) {\n            setCurrentState(0);\n            return;\n        }\n        const timeout = setTimeout(()=>{\n            setCurrentState((prevState)=>loop ? prevState === loadingStates.length - 1 ? 0 : prevState + 1 : Math.min(prevState + 1, loadingStates.length - 1));\n        }, duration);\n        return ()=>clearTimeout(timeout);\n    }, [\n        currentState,\n        loading,\n        loop,\n        loadingStates.length,\n        duration\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.AnimatePresence, {\n        mode: \"wait\",\n        children: loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n            initial: {\n                opacity: 0\n            },\n            animate: {\n                opacity: 1\n            },\n            exit: {\n                opacity: 0\n            },\n            className: \"w-full h-full fixed inset-0 z-[100] flex items-center justify-center backdrop-blur-2xl\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"h-96  relative\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LoaderCore, {\n                        value: currentState,\n                        loadingStates: loadingStates\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\sc_copilot_ai\\\\components\\\\ui\\\\multi-step-loader.tsx\",\n                        lineNumber: 138,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\sc_copilot_ai\\\\components\\\\ui\\\\multi-step-loader.tsx\",\n                    lineNumber: 137,\n                    columnNumber: 11\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gradient-to-t inset-x-0 z-20 bottom-0 bg-white dark:bg-black h-full absolute [mask-image:radial-gradient(900px_at_center,transparent_30%,white)]\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\sc_copilot_ai\\\\components\\\\ui\\\\multi-step-loader.tsx\",\n                    lineNumber: 141,\n                    columnNumber: 11\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\sc_copilot_ai\\\\components\\\\ui\\\\multi-step-loader.tsx\",\n            lineNumber: 125,\n            columnNumber: 9\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\sc_copilot_ai\\\\components\\\\ui\\\\multi-step-loader.tsx\",\n        lineNumber: 123,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/multi-step-loader.tsx\n");

/***/ }),

/***/ "(ssr)/./lib/utils.ts":
/*!**********************!*\
  !*** ./lib/utils.ts ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9saWIvdXRpbHMudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQTRDO0FBQ0o7QUFFakMsU0FBU0UsR0FBRyxHQUFHQyxNQUFvQjtJQUN4QyxPQUFPRix1REFBT0EsQ0FBQ0QsMENBQUlBLENBQUNHO0FBQ3RCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc3VwcGx5LWNoYWluLWRhc2hib2FyZC8uL2xpYi91dGlscy50cz9mNzQ1Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHR5cGUgQ2xhc3NWYWx1ZSwgY2xzeCB9IGZyb20gXCJjbHN4XCJcbmltcG9ydCB7IHR3TWVyZ2UgfSBmcm9tIFwidGFpbHdpbmQtbWVyZ2VcIlxuXG5leHBvcnQgZnVuY3Rpb24gY24oLi4uaW5wdXRzOiBDbGFzc1ZhbHVlW10pIHtcbiAgcmV0dXJuIHR3TWVyZ2UoY2xzeChpbnB1dHMpKVxufSJdLCJuYW1lcyI6WyJjbHN4IiwidHdNZXJnZSIsImNuIiwiaW5wdXRzIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./lib/utils.ts\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"4ceaa42a8344\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zdXBwbHktY2hhaW4tZGFzaGJvYXJkLy4vYXBwL2dsb2JhbHMuY3NzPzk2YjkiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI0Y2VhYTQyYTgzNDRcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n\n\n\nconst metadata = {\n    title: \"Supply Chain AI Dashboard\",\n    description: \"AI-powered supply chain automation dashboard\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default().className),\n            children: children\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\sc_copilot_ai\\\\app\\\\layout.tsx\",\n            lineNumber: 19,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\sc_copilot_ai\\\\app\\\\layout.tsx\",\n        lineNumber: 18,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvbGF5b3V0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUlNQTtBQUZnQjtBQUlmLE1BQU1DLFdBQXFCO0lBQ2hDQyxPQUFPO0lBQ1BDLGFBQWE7QUFDZixFQUFDO0FBRWMsU0FBU0MsV0FBVyxFQUNqQ0MsUUFBUSxFQUdUO0lBQ0MscUJBQ0UsOERBQUNDO1FBQUtDLE1BQUs7a0JBQ1QsNEVBQUNDO1lBQUtDLFdBQVdULDJKQUFlO3NCQUFHSzs7Ozs7Ozs7Ozs7QUFHekMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zdXBwbHktY2hhaW4tZGFzaGJvYXJkLy4vYXBwL2xheW91dC50c3g/OTk4OCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgdHlwZSB7IE1ldGFkYXRhIH0gZnJvbSAnbmV4dCdcbmltcG9ydCB7IEludGVyIH0gZnJvbSAnbmV4dC9mb250L2dvb2dsZSdcbmltcG9ydCAnLi9nbG9iYWxzLmNzcydcblxuY29uc3QgaW50ZXIgPSBJbnRlcih7IHN1YnNldHM6IFsnbGF0aW4nXSB9KVxuXG5leHBvcnQgY29uc3QgbWV0YWRhdGE6IE1ldGFkYXRhID0ge1xuICB0aXRsZTogJ1N1cHBseSBDaGFpbiBBSSBEYXNoYm9hcmQnLFxuICBkZXNjcmlwdGlvbjogJ0FJLXBvd2VyZWQgc3VwcGx5IGNoYWluIGF1dG9tYXRpb24gZGFzaGJvYXJkJyxcbn1cblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gUm9vdExheW91dCh7XG4gIGNoaWxkcmVuLFxufToge1xuICBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlXG59KSB7XG4gIHJldHVybiAoXG4gICAgPGh0bWwgbGFuZz1cImVuXCI+XG4gICAgICA8Ym9keSBjbGFzc05hbWU9e2ludGVyLmNsYXNzTmFtZX0+e2NoaWxkcmVufTwvYm9keT5cbiAgICA8L2h0bWw+XG4gIClcbn0iXSwibmFtZXMiOlsiaW50ZXIiLCJtZXRhZGF0YSIsInRpdGxlIiwiZGVzY3JpcHRpb24iLCJSb290TGF5b3V0IiwiY2hpbGRyZW4iLCJodG1sIiwibGFuZyIsImJvZHkiLCJjbGFzc05hbWUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\projects\sc_copilot_ai\app\page.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/framer-motion","vendor-chunks/lucide-react","vendor-chunks/@swc","vendor-chunks/tailwind-merge","vendor-chunks/clsx"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5Cumesh%5Cprojects%5Csc_copilot_ai%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cumesh%5Cprojects%5Csc_copilot_ai&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();