'use client'

import React from 'react'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { LineChart, Line, XAxis, YAxis, ResponsiveContainer } from 'recharts'
import { MessageSquare, TrendingUp } from 'lucide-react'

const revenueData = [
  { month: '2024', value: 1.2, trend: 20 },
  { month: 'Mar', value: 2.1, trend: 35 },
  { month: 'Apr', value: 1.8, trend: 28 },
  { month: 'May', value: 3.2, trend: 45 },
  { month: 'Jun', value: 2.8, trend: 38 },
  { month: 'Jul', value: 4.1, trend: 55 },
  { month: 'Aug', value: 3.5, trend: 48 },
  { month: 'Sep', value: 5.33, trend: 65 },
  { month: 'Oct', value: 4.8, trend: 58 },
  { month: 'Nov', value: 2.43, trend: 35 },
  { month: 'Dec', value: 1.2, trend: 22 },
  { month: '15 Jan', value: 1.8, trend: 28 },
]

export function RevenueTrend() {
  return (
    <Card className="glass-effect col-span-2">
      <CardHeader className="pb-2">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 rounded-lg bg-slate-800 flex items-center justify-center">
              <MessageSquare className="w-4 h-4 text-blue-400" />
            </div>
            <div>
              <CardTitle className="text-sm font-medium text-slate-300">Revenue trend</CardTitle>
              <div className="text-xs text-slate-500">Summary Statistics</div>
            </div>
          </div>
          <div className="w-8 h-8 rounded-full bg-slate-800 flex items-center justify-center">
            <TrendingUp className="w-4 h-4 text-green-400" />
          </div>
        </div>
        
        {/* Stats Row */}
        <div className="flex items-center space-x-6 mt-4">
          <div className="text-center">
            <div className="text-lg font-bold text-white">1.2 <span className="text-xs text-slate-400">Mn</span></div>
          </div>
          <div className="text-center">
            <div className="text-lg font-bold text-white">5.33 <span className="text-xs text-slate-400">Max</span></div>
          </div>
          <div className="text-center">
            <div className="text-lg font-bold text-white">2.43 <span className="text-xs text-slate-400">Average</span></div>
          </div>
          <div className="text-center">
            <div className="text-lg font-bold text-white">1 <span className="text-xs text-slate-400">Day</span></div>
          </div>
          <div className="text-center">
            <div className="text-lg font-bold text-white">1 <span className="text-xs text-slate-400">Week</span></div>
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="pt-0">
        <div className="h-40 relative">
          <ResponsiveContainer width="100%" height="100%">
            <LineChart data={revenueData}>
              <defs>
                <linearGradient id="revenueGradient" x1="0" y1="0" x2="0" y2="1">
                  <stop offset="0%" stopColor="#3b82f6" stopOpacity={0.3} />
                  <stop offset="100%" stopColor="#3b82f6" stopOpacity={0} />
                </linearGradient>
                <pattern id="diagonalHatch" patternUnits="userSpaceOnUse" width="4" height="4">
                  <path d="M 0,4 l 4,-4 M -1,1 l 2,-2 M 3,5 l 2,-2" stroke="#8b5cf6" strokeWidth="1" opacity="0.3"/>
                </pattern>
              </defs>
              <XAxis 
                dataKey="month" 
                axisLine={false}
                tickLine={false}
                tick={{ fontSize: 10, fill: '#64748b' }}
              />
              <YAxis hide />
              <Line
                type="monotone"
                dataKey="value"
                stroke="#3b82f6"
                strokeWidth={2}
                dot={{ fill: '#3b82f6', strokeWidth: 0, r: 3 }}
                activeDot={{ r: 4, fill: '#3b82f6' }}
              />
            </LineChart>
          </ResponsiveContainer>
          
          {/* Diagonal pattern overlay */}
          <div className="absolute inset-0 pointer-events-none">
            <svg className="w-full h-full opacity-20">
              <defs>
                <pattern id="diagonalLines" patternUnits="userSpaceOnUse" width="8" height="8">
                  <path d="M 0,8 l 8,-8 M -2,2 l 4,-4 M 6,10 l 4,-4" stroke="#8b5cf6" strokeWidth="1"/>
                </pattern>
              </defs>
              <rect width="100%" height="60%" fill="url(#diagonalLines)" />
            </svg>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}