'use client'

import React, { useState } from 'react'
import { motion } from 'framer-motion'
import { MultiStepLoader as Loader } from '@/components/ui/multi-step-loader'
import { HoverBorderGradient } from '@/components/ui/hover-border-gradient'
import { X } from 'lucide-react'
const SupplyChainLogo = () => {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className="h-4 w-4 text-white"
    >
      <path
        d="M12 2L2 7L12 12L22 7L12 2Z"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M2 17L12 22L22 17"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M2 12L12 17L22 12"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};
export function AIAssistant() {
  const [loading, setLoading] = useState(false);
  
  const loadingStates = [
    {
      text: "Analyzing supply chain data",
    },
    {
      text: "Processing inventory levels",
    },
    {
      text: "Calculating demand forecasts",
    },
    {
      text: "Optimizing delivery routes",
    },
    {
      text: "Updating supplier metrics",
    },
    {
      text: "Generating insights report",
    },
    {
      text: "Finalizing recommendations",
    },
    {
      text: "Dashboard ready!",
    },
  ];

  return (
    <motion.div
      className="glass-card relative h-full w-full p-8 lg:p-12 flex flex-col items-center justify-center overflow-hidden min-h-[400px]"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.8 }}
      whileHover={{ scale: 1.02 }}
    >
      <div className="relative mb-8 lg:mb-12">
        {/* AI Brain Sphere Animation - Larger */}
        <div className="ai-brain-sphere scale-125 lg:scale-150">
          <div className="brain-container">
            <div className="brain-mesh"></div>
            <div className="brain-waves"></div>
            <div className="brain-core"></div>
            <div className="brain-particles">
              <div className="particle"></div>
              <div className="particle"></div>
              <div className="particle"></div>
              <div className="particle"></div>
              <div className="particle"></div>
              <div className="particle"></div>
            </div>
          </div>
        </div>
      </div>

      {/* Multi-step loader */}
      <div className="mb-8">
        <Loader loadingStates={loadingStates} loading={loading} duration={2000} />
      </div>

      <HoverBorderGradient
        containerClassName="rounded-full mb-6"
        as="button"
        className="glass-effect text-white flex items-center space-x-3 px-8 py-3 text-lg hover:bg-white/20 transition-all duration-300"
        onClick={() => setLoading(true)}
      >
        <SupplyChainLogo />
        <span>Start AI Analysis</span>
      </HoverBorderGradient>

      {loading && (
        <button
          className="fixed top-4 right-4 text-black dark:text-white z-[120]"
          onClick={() => setLoading(false)}
        >
          <X className="h-10 w-10" />
        </button>
      )}

      <div className="text-center max-w-2xl">
        <h2 className="text-2xl lg:text-3xl font-bold text-white mb-4">
          AI Supply Chain Assistant
        </h2>
        <h3 className="text-lg lg:text-xl font-semibold text-violet-300 mb-4">
          Analyze product sales over last year
        </h3>
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 text-sm text-white/70">
          <div className="flex flex-col items-center">
            <div className="w-8 h-8 glass-effect rounded-full flex items-center justify-center mb-2">
              <span className="text-violet-300">📊</span>
            </div>
            <span>Analytics</span>
          </div>
          <div className="flex flex-col items-center">
            <div className="w-8 h-8 glass-effect rounded-full flex items-center justify-center mb-2">
              <span className="text-violet-300">🔮</span>
            </div>
            <span>Forecasting</span>
          </div>
          <div className="flex flex-col items-center">
            <div className="w-8 h-8 glass-effect rounded-full flex items-center justify-center mb-2">
              <span className="text-violet-300">⚡</span>
            </div>
            <span>Optimization</span>
          </div>
          <div className="flex flex-col items-center">
            <div className="w-8 h-8 glass-effect rounded-full flex items-center justify-center mb-2">
              <span className="text-violet-300">🎯</span>
            </div>
            <span>Insights</span>
          </div>
        </div>
      </div>
    </motion.div>
  )
}