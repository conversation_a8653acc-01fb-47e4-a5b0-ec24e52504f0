import { NextRequest, NextResponse } from 'next/server'
import { aiInsights, supplyChainMetrics } from '@/lib/demo-data'

export async function GET(request: NextRequest) {
  try {
    // Simulate AI processing delay
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // In a real application, this would call an AI service
    const insights = {
      summary: "Supply chain performance is strong with room for optimization in international markets.",
      metrics: supplyChainMetrics,
      recommendations: aiInsights,
      confidence: 0.87,
      lastUpdated: new Date().toISOString()
    }
    
    return NextResponse.json(insights)
  } catch (error) {
    return NextResponse.json(
      { error: 'Failed to generate AI insights' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { query } = body
    
    // Simulate AI query processing
    await new Promise(resolve => setTimeout(resolve, 1500))
    
    // Mock AI response based on query
    const response = {
      query,
      answer: `Based on your supply chain data, here's what I found: ${query.toLowerCase().includes('sales') ? 'Sales are performing 15% above target' : 'Supply chain efficiency is at 87.3%'}`,
      confidence: 0.92,
      sources: ['Sales Dashboard', 'Inventory Management', 'Supplier Network'],
      timestamp: new Date().toISOString()
    }
    
    return NextResponse.json(response)
  } catch (error) {
    return NextResponse.json(
      { error: 'Failed to process AI query' },
      { status: 500 }
    )
  }
}