import { cn } from "@/lib/utils";
import { Menu } from "lucide-react";
import {
  AnimatePresence,
  MotionValue,
  motion,
  useMotionValue,
  useSpring,
  useTransform,
} from "framer-motion";

import { useRef, useState, memo, useCallback } from "react";

export const FloatingDock = ({
  items,
  desktopClassName,
  mobileClassName,
}: {
  items: { title: string; icon: React.ReactNode; href: string }[];
  desktopClassName?: string;
  mobileClassName?: string;
}) => {
  return (
    <>
      <FloatingDockDesktop items={items} className={desktopClassName} />
      <FloatingDockMobile items={items} className={mobileClassName} />
    </>
  );
};

const FloatingDockMobile = ({
  items,
  className,
}: {
  items: { title: string; icon: React.ReactNode; href: string }[];
  className?: string;
}) => {
  const [open, setOpen] = useState(false);
  return (
    <div className={cn("relative block md:hidden", className)}>
      <AnimatePresence>
        {open && (
          <motion.div
            layoutId="nav"
            className="absolute inset-x-0 bottom-full mb-2 flex flex-col gap-2"
          >
            {items.map((item, idx) => (
              <motion.div
                key={item.title}
                initial={{ opacity: 0, y: 10 }}
                animate={{
                  opacity: 1,
                  y: 0,
                }}
                exit={{
                  opacity: 0,
                  y: 10,
                  transition: {
                    delay: idx * 0.05,
                  },
                }}
                transition={{ delay: (items.length - 1 - idx) * 0.05 }}
              >
                <a
                  href={item.href}
                  key={item.title}
                  className="flex h-10 w-10 items-center justify-center rounded-full glass-effect hover:bg-blue-500/20 transition-colors duration-200"
                  style={{
                    border: '1px solid rgba(59, 130, 246, 0.3)',
                    boxShadow: '0 0 8px rgba(59, 130, 246, 0.15)',
                  }}
                >
                  <div className="h-4 w-4">{item.icon}</div>
                </a>
              </motion.div>
            ))}
          </motion.div>
        )}
      </AnimatePresence>
      <button
        onClick={() => setOpen(!open)}
        className="flex h-10 w-10 items-center justify-center rounded-full glass-effect hover:bg-blue-500/20 transition-colors duration-200"
        style={{
          border: '1px solid rgba(59, 130, 246, 0.3)',
          boxShadow: '0 0 8px rgba(59, 130, 246, 0.15)',
        }}
      >
        <Menu className="h-5 w-5 text-slate-300" />
      </button>
    </div>
  );
};

const FloatingDockDesktop = ({
  items,
  className,
}: {
  items: { title: string; icon: React.ReactNode; href: string }[];
  className?: string;
}) => {
  let mouseX = useMotionValue(Infinity);
  
  const handleMouseMove = useCallback((e: React.MouseEvent) => {
    mouseX.set(e.pageX);
  }, [mouseX]);
  
  const handleMouseLeave = useCallback(() => {
    mouseX.set(Infinity);
  }, [mouseX]);
  
  return (
    <motion.div
      onMouseMove={handleMouseMove}
      onMouseLeave={handleMouseLeave}
      className={cn(
        "mx-auto hidden h-16 items-end gap-3 rounded-2xl px-4 pb-3 md:flex glass-effect",
        className,
      )}
      style={{
        border: '1px solid rgba(59, 130, 246, 0.3)',
        boxShadow: '0 0 15px rgba(59, 130, 246, 0.15), inset 0 1px 0 rgba(255, 255, 255, 0.1)',
        background: 'rgba(15, 23, 42, 0.85)',
        backdropFilter: 'blur(16px)',
      }}
    >
      {items.map((item) => (
        <IconContainer mouseX={mouseX} key={item.title} {...item} />
      ))}
    </motion.div>
  );
};

const IconContainer = memo(function IconContainer({
  mouseX,
  title,
  icon,
  href,
}: {
  mouseX: MotionValue;
  title: string;
  icon: React.ReactNode;
  href: string;
}) {
  let ref = useRef<HTMLDivElement>(null);

  let distance = useTransform(mouseX, (val) => {
    let bounds = ref.current?.getBoundingClientRect() ?? { x: 0, width: 0 };
    return val - bounds.x - bounds.width / 2;
  });

  let widthTransform = useTransform(distance, [-100, 0, 100], [40, 70, 40]);
  let heightTransform = useTransform(distance, [-100, 0, 100], [40, 70, 40]);

  let widthTransformIcon = useTransform(distance, [-100, 0, 100], [20, 35, 20]);
  let heightTransformIcon = useTransform(distance, [-100, 0, 100], [20, 35, 20]);

  let width = useSpring(widthTransform, {
    mass: 0.05,
    stiffness: 300,
    damping: 15,
  });
  let height = useSpring(heightTransform, {
    mass: 0.05,
    stiffness: 300,
    damping: 15,
  });

  let widthIcon = useSpring(widthTransformIcon, {
    mass: 0.05,
    stiffness: 300,
    damping: 15,
  });
  let heightIcon = useSpring(heightTransformIcon, {
    mass: 0.05,
    stiffness: 300,
    damping: 15,
  });

  const [hovered, setHovered] = useState(false);

  return (
    <a href={href}>
      <motion.div
        ref={ref}
        style={{ width, height }}
        onMouseEnter={() => setHovered(true)}
        onMouseLeave={() => setHovered(false)}
        className="relative flex aspect-square items-center justify-center rounded-full glass-effect hover:bg-blue-500/20 transition-colors duration-200"
        whileHover={{
          boxShadow: "0 0 15px rgba(59, 130, 246, 0.3)",
        }}
        transition={{ duration: 0.2 }}
      >
        <AnimatePresence>
          {hovered && (
            <motion.div
              initial={{ opacity: 0, y: 8, x: "-50%" }}
              animate={{ opacity: 1, y: 0, x: "-50%" }}
              exit={{ opacity: 0, y: 4, x: "-50%" }}
              transition={{ duration: 0.15 }}
              className="absolute -top-8 left-1/2 w-fit rounded-md px-2 py-0.5 text-xs whitespace-pre text-white glass-effect"
              style={{
                border: '1px solid rgba(59, 130, 246, 0.3)',
                boxShadow: '0 0 8px rgba(59, 130, 246, 0.2)',
                background: 'rgba(15, 23, 42, 0.95)',
                backdropFilter: 'blur(8px)',
              }}
            >
              {title}
            </motion.div>
          )}
        </AnimatePresence>
        <motion.div
          style={{ width: widthIcon, height: heightIcon }}
          className="flex items-center justify-center"
        >
          {icon}
        </motion.div>
      </motion.div>
    </a>
  );
});