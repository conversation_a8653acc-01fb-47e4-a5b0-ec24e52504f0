@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: 250 100% 97%;
  --foreground: 0 0% 100%;
  --card: 250 100% 97%;
  --card-foreground: 0 0% 100%;
  --popover: 250 100% 97%;
  --popover-foreground: 0 0% 100%;
  --primary: 262 83% 58%;
  --primary-foreground: 0 0% 100%;
  --secondary: 270 50% 40%;
  --secondary-foreground: 0 0% 100%;
  --muted: 270 50% 40%;
  --muted-foreground: 0 0% 80%;
  --accent: 280 100% 70%;
  --accent-foreground: 0 0% 100%;
  --destructive: 0 62.8% 30.6%;
  --destructive-foreground: 0 0% 100%;
  --border: 270 50% 40%;
  --input: 270 50% 40%;
  --ring: 262 83% 58%;
  --radius: 1rem;

  /* New theme colors */
  --glass-bg: rgba(255, 255, 255, 0.1);
  --glass-border: rgba(255, 255, 255, 0.2);
  --purple-primary: #8b5cf6;
  --purple-secondary: #a855f7;
  --purple-accent: #c084fc;
  --violet-primary: #7c3aed;
  --violet-secondary: #8b5cf6;
}

* {
  border-color: hsl(var(--border));
}

body {
  color: hsl(var(--foreground));
  background: linear-gradient(135deg, #667eea 0%, #764ba2 25%, #8b5cf6 50%, #a855f7 75%, #c084fc 100%);
  min-height: 100vh;
  position: relative;
  overflow-x: hidden;
}

body::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 15% 25%, rgba(139, 92, 246, 0.4) 0%, transparent 60%),
    radial-gradient(circle at 85% 75%, rgba(168, 85, 247, 0.4) 0%, transparent 60%),
    radial-gradient(circle at 50% 50%, rgba(124, 58, 237, 0.3) 0%, transparent 70%),
    radial-gradient(circle at 30% 80%, rgba(192, 132, 252, 0.3) 0%, transparent 50%);
  pointer-events: none;
  z-index: -1;
}

.glass-effect {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow:
    0 8px 32px rgba(139, 92, 246, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.glass-card {
  background: rgba(255, 255, 255, 0.08);
  backdrop-filter: blur(24px);
  border: 1px solid rgba(255, 255, 255, 0.15);
  border-radius: 1.5rem;
  box-shadow:
    0 12px 40px rgba(139, 92, 246, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.15);
}

.exam-card {
  background: rgba(255, 255, 255, 0.12);
  backdrop-filter: blur(16px);
  border: 1px solid rgba(255, 255, 255, 0.18);
  border-radius: 1rem;
  box-shadow:
    0 8px 24px rgba(139, 92, 246, 0.18),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.gradient-border {
  position: relative;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(147, 51, 234, 0.1));
  border: 1px solid transparent;
}

.gradient-border::before {
  content: '';
  position: absolute;
  inset: 0;
  padding: 1px;
  background: linear-gradient(135deg, #3b82f6, #9333ea);
  border-radius: inherit;
  mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  mask-composite: xor;
  -webkit-mask-composite: xor;
}

.ai-glow {
  box-shadow: 
    0 0 50px rgba(59, 130, 246, 0.3),
    0 0 100px rgba(59, 130, 246, 0.1),
    inset 0 0 50px rgba(59, 130, 246, 0.05);
}

.ai-sphere {
  background: radial-gradient(
    circle at 30% 30%,
    rgba(96, 165, 250, 0.9) 0%,
    rgba(59, 130, 246, 0.8) 25%,
    rgba(30, 64, 175, 0.7) 50%,
    rgba(30, 58, 138, 0.6) 75%,
    rgba(15, 23, 42, 0.8) 100%
  );
  position: relative;
  overflow: hidden;
}

.ai-sphere::before {
  content: '';
  position: absolute;
  top: 10%;
  left: 20%;
  width: 40%;
  height: 40%;
  background: radial-gradient(
    circle,
    rgba(255, 255, 255, 0.3) 0%,
    rgba(255, 255, 255, 0.1) 50%,
    transparent 100%
  );
  border-radius: 50%;
  filter: blur(10px);
}

.ai-sphere::after {
  content: '';
  position: absolute;
  inset: 0;
  border-radius: 50%;
  background: linear-gradient(
    135deg,
    rgba(96, 165, 250, 0.2) 0%,
    transparent 50%,
    rgba(30, 64, 175, 0.3) 100%
  );
}

.chart-gradient {
  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 50%, #ec4899 100%);
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

.shimmer {
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.1),
    transparent
  );
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}

/* AI Assistant Brain Sphere Animation */
.ai-brain-sphere {
  position: relative;
  width: 160px;
  height: 160px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.brain-container {
  position: relative;
  width: 140px;
  height: 140px;
  border-radius: 50%;
  background: radial-gradient(circle at 30% 30%, #1e40af, #1e3a8a, #0f172a);
  overflow: hidden;
  box-shadow: 
    0 0 40px rgba(59, 130, 246, 0.3),
    inset 0 0 40px rgba(59, 130, 246, 0.1);
}

.brain-mesh {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: 
    /* Horizontal flowing lines */
    linear-gradient(90deg, transparent 0%, rgba(96, 165, 250, 0.4) 20%, transparent 40%, rgba(147, 197, 253, 0.3) 60%, transparent 80%, rgba(59, 130, 246, 0.5) 100%),
    /* Vertical flowing lines */
    linear-gradient(0deg, transparent 0%, rgba(147, 197, 253, 0.3) 25%, transparent 50%, rgba(96, 165, 250, 0.4) 75%, transparent 100%),
    /* Diagonal mesh pattern */
    linear-gradient(45deg, transparent 30%, rgba(59, 130, 246, 0.2) 50%, transparent 70%),
    linear-gradient(-45deg, transparent 30%, rgba(147, 197, 253, 0.2) 50%, transparent 70%);
  background-size: 
    100% 20px,
    20px 100%,
    40px 40px,
    40px 40px;
  border-radius: 50%;
  animation: brainFlow 4s ease-in-out infinite;
}

.brain-waves {
  position: absolute;
  top: 10%;
  left: 10%;
  right: 10%;
  bottom: 10%;
  background: 
    radial-gradient(ellipse at 20% 30%, rgba(96, 165, 250, 0.6) 0%, transparent 50%),
    radial-gradient(ellipse at 80% 70%, rgba(147, 197, 253, 0.4) 0%, transparent 50%),
    radial-gradient(ellipse at 50% 20%, rgba(59, 130, 246, 0.3) 0%, transparent 60%),
    radial-gradient(ellipse at 30% 80%, rgba(96, 165, 250, 0.5) 0%, transparent 40%);
  border-radius: 50%;
  animation: waveFlow 6s ease-in-out infinite reverse;
}

.brain-core {
  position: absolute;
  top: 25%;
  left: 25%;
  right: 25%;
  bottom: 25%;
  background: radial-gradient(circle, rgba(147, 197, 253, 0.8) 0%, rgba(59, 130, 246, 0.4) 50%, transparent 100%);
  border-radius: 50%;
  animation: coreGlow 3s ease-in-out infinite;
}

.brain-particles {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 50%;
}

.particle {
  position: absolute;
  width: 2px;
  height: 2px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  box-shadow: 0 0 4px rgba(96, 165, 250, 0.8);
  animation: particleFloat 3s ease-in-out infinite;
}

.particle:nth-child(1) { top: 25%; left: 35%; animation-delay: 0s; }
.particle:nth-child(2) { top: 35%; right: 30%; animation-delay: 0.5s; }
.particle:nth-child(3) { bottom: 35%; left: 30%; animation-delay: 1s; }
.particle:nth-child(4) { bottom: 30%; right: 35%; animation-delay: 1.5s; }
.particle:nth-child(5) { top: 45%; left: 55%; animation-delay: 2s; }
.particle:nth-child(6) { top: 55%; right: 45%; animation-delay: 2.5s; }

@keyframes brainFlow {
  0%, 100% {
    transform: rotate(0deg) scale(1);
    opacity: 0.8;
  }
  25% {
    transform: rotate(90deg) scale(1.05);
    opacity: 0.9;
  }
  50% {
    transform: rotate(180deg) scale(0.95);
    opacity: 1;
  }
  75% {
    transform: rotate(270deg) scale(1.02);
    opacity: 0.9;
  }
}

@keyframes waveFlow {
  0%, 100% {
    transform: scale(1) rotate(0deg);
    opacity: 0.6;
  }
  33% {
    transform: scale(1.1) rotate(120deg);
    opacity: 0.8;
  }
  66% {
    transform: scale(0.9) rotate(240deg);
    opacity: 0.7;
  }
}

@keyframes coreGlow {
  0%, 100% {
    transform: scale(1);
    opacity: 0.6;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.9;
  }
}

@keyframes particleFloat {
  0%, 100% {
    opacity: 0.4;
    transform: translateY(0px) scale(0.8);
  }
  50% {
    opacity: 1;
    transform: translateY(-10px) scale(1.2);
  }
}