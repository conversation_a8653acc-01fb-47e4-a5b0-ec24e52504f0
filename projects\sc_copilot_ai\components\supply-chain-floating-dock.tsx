import React from "react";
import { FloatingDock } from "@/components/ui/floating-dock";
import {
  Home,
  Package,
  Truck,
  BarChart3,
  Settings,
  Users,
  MessageSquare,
} from "lucide-react";

export default function SupplyChainFloatingDock() {
  const links = [
    {
      title: "Dashboard",
      icon: (
        <Home className="h-full w-full text-slate-300 dark:text-slate-300" />
      ),
      href: "/",
    },
    {
      title: "Inventory",
      icon: (
        <Package className="h-full w-full text-slate-300 dark:text-slate-300" />
      ),
      href: "#inventory",
    },
    {
      title: "Logistics",
      icon: (
        <Truck className="h-full w-full text-slate-300 dark:text-slate-300" />
      ),
      href: "#logistics",
    },
    {
      title: "Analytics",
      icon: (
        <BarChart3 className="h-full w-full text-slate-300 dark:text-slate-300" />
      ),
      href: "#analytics",
    },
    {
      title: "AI Assistant",
      icon: (
        <MessageSquare className="h-full w-full text-blue-400 dark:text-blue-400" />
      ),
      href: "#ai-chat",
    },
    {
      title: "Suppliers",
      icon: (
        <Users className="h-full w-full text-slate-300 dark:text-slate-300" />
      ),
      href: "#suppliers",
    },
    {
      title: "Settings",
      icon: (
        <Settings className="h-full w-full text-slate-300 dark:text-slate-300" />
      ),
      href: "#settings",
    },
  ];
  
  return (
    <div className="fixed bottom-6 left-1/2 transform -translate-x-1/2 z-50">
      <FloatingDock
        items={links}
      />
    </div>
  );
}